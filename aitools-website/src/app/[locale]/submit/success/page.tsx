import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import mongoose from 'mongoose';
import { CheckCircle, CreditCard, Calendar } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import { Locale } from '@/i18n/config';

interface PageProps {
  params: Promise<{ locale: Locale }>;
  searchParams: Promise<{ toolId?: string; paid?: string }>;
}

async function getToolData(toolId: string, userEmail: string) {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(toolId)) {
      return null;
    }

    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return null;
    }

    const tool = await Tool.findById(toolId);
    if (!tool) {
      return null;
    }

    // 检查工具所有权
    if (!tool.submittedBy || tool.submittedBy.toString() !== user._id.toString()) {
      return null;
    }

    return {
      _id: tool._id.toString(),
      name: tool.name,
      status: tool.status,
      launchOption: tool.launchOption,
      selectedLaunchDate: tool.selectedLaunchDate ? tool.selectedLaunchDate.toISOString() : null,
    };
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return null;
  }
}

export default async function SubmitSuccessPage({ params, searchParams }: PageProps) {
  const session = await getServerSession(authOptions);
  const { locale } = await params;
  const { toolId, paid } = await searchParams;

  // 获取翻译
  const t = await getTranslations('submit.success');

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect('/');
  }

  // 检查是否有工具ID
  if (!toolId) {
    redirect('/');
  }

  const isPaid = paid === 'true';

  // 获取工具数据
  const tool = await getToolData(toolId, session.user.email);

  if (!tool) {
    redirect('/');
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Success Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 rounded-full p-3">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isPaid ? t('payment_success_title') : t('submit_success_title')}
        </h1>

        <p className="text-lg text-gray-600">
          {isPaid ? t('payment_success_desc') : t('submit_success_desc')}
        </p>
      </div>

      {/* Tool Info */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('tool_info_title')}</h2>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">{t('tool_name_label')}</span>
            <span className="font-medium">{tool.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">{t('current_status_label')}</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              tool.status === 'draft' ? 'bg-gray-100 text-gray-800' :
              tool.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              tool.status === 'approved' ? 'bg-green-100 text-green-800' :
              'bg-red-100 text-red-800'
            }`}>
              {tool.status === 'draft' ? t('status_draft') :
               tool.status === 'pending' ? t('status_pending') :
               tool.status === 'approved' ? t('status_approved') : t('status_rejected')}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">{t('launch_option_label')}</span>
            <span className="font-medium">
              {tool.launchOption === 'paid' ? t('launch_option_paid') : t('launch_option_free')}
            </span>
          </div>
          {tool.selectedLaunchDate && (
            <div className="flex justify-between">
              <span className="text-gray-600">{t('planned_launch_date_label')}</span>
              <span className="font-medium">
                {new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Launch Date Management - 所有用户都可以看到 */}
      <div className={`${tool.launchOption === 'paid' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'} border rounded-lg p-6 mb-8`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {tool.launchOption === 'paid' ? (
              <>
                <CreditCard className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-blue-900">
                  {t('premium_service_title')}
                </h3>
              </>
            ) : (
              <>
                <Calendar className="h-6 w-6 text-gray-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('launch_date_management_title')}
                </h3>
              </>
            )}
          </div>

              {/* 修改发布日期按钮 - 暂时移除客户端组件 */}
          {['pending', 'approved'].includes(tool.status) && (
            <div className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
              {t('edit_launch_date')}
            </div>
          )}
        </div>

        {/* 服务特性 - 只有付费用户显示 */}
        {tool.launchOption === 'paid' && (
          <div className="grid md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('premium_features.priority_review')}</span>
              </div>
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('premium_features.homepage_featured')}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('premium_features.custom_launch_date')}</span>
              </div>
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('premium_features.dedicated_support')}</span>
              </div>
            </div>
          </div>
        )}

        {/* 免费用户的服务说明 */}
        {tool.launchOption === 'free' && (
          <div className="bg-gray-100 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">{t('free_service_title')}</h4>
            <div className="space-y-2">
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('free_features.standard_review')}</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('free_features.flexible_date')}</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{t('free_features.standard_position')}</span>
              </div>
            </div>
          </div>
        )}

        {/* 当前发布日期显示 */}
        {tool.selectedLaunchDate && (
          <div className={`bg-white rounded-lg p-4 border ${tool.launchOption === 'paid' ? 'border-blue-200' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className={`h-5 w-5 mr-2 ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`} />
                <span className="text-sm font-medium text-gray-900">{t('current_launch_date_label')}</span>
              </div>
              <span className={`text-sm font-semibold ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`}>
                {new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
              </span>
            </div>
            {['pending', 'approved'].includes(tool.status) && (
              <p className="text-xs text-gray-600 mt-2">
                {t('launch_date_tip')}
                {tool.launchOption === 'free' && t('launch_date_tip_free')}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Contact Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {t('help_title')}
        </h3>
        <p className="text-gray-600 mb-4">
          {t('help_desc')}
        </p>
        <div className="space-y-2 text-sm text-gray-600">
          <div>{t('contact_email')}</div>
        </div>
      </div>

      {/* Action Buttons - 简化版本 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <div className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium text-center">
          {t('view_submissions')}
        </div>
        <div className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center">
          {t('back_to_home')}
        </div>
      </div>
    </div>
  );
}

